{"version": 2, "from_file_path": "/Users/<USER>/Programming/jobs/hopaggregator/hopfun/.conductor/bangalore/contracts/hopfun/sources/math.move", "definition_location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 15, "end": 19}, "module_name": ["497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac", "math"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 27, "end": 178}, "definition_location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 47, "end": 61}, "type_parameters": [], "parameters": [["amount_in#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 62, "end": 71}], ["fee_bps#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 78, "end": 85}]], "returns": [{"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 93, "end": 96}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 110, "end": 119}, "1": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 110, "end": 127}, "2": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 132, "end": 139}, "3": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 132, "end": 147}, "4": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 129, "end": 130}, "5": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 153, "end": 163}, "6": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 150, "end": 151}, "7": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 107, "end": 172}}, "is_native": false}, "1": {"location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 184, "end": 471}, "definition_location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 204, "end": 224}, "type_parameters": [], "parameters": [["for_amount_in#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 225, "end": 238}], ["fee_bps#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 245, "end": 252}]], "returns": [{"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 260, "end": 263}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 397, "end": 410}, "1": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 413, "end": 419}, "2": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 411, "end": 412}, "3": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 397, "end": 427}, "4": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 432, "end": 438}, "5": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 441, "end": 448}, "6": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 439, "end": 440}, "7": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 432, "end": 456}, "8": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 429, "end": 430}, "9": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 395, "end": 465}}, "is_native": false}, "2": {"location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 477, "end": 768}, "definition_location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 497, "end": 511}, "type_parameters": [], "parameters": [["amount_in#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 512, "end": 521}], ["reserve_in#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 528, "end": 538}], ["reserve_out#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 545, "end": 556}]], "returns": [{"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 564, "end": 567}], "locals": [["denominator#1#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 657, "end": 668}], ["numerator#1#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 582, "end": 591}]], "nops": {}, "code_map": {"0": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 601, "end": 610}, "1": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 601, "end": 618}, "2": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 623, "end": 634}, "3": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 623, "end": 642}, "4": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 620, "end": 621}, "5": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 582, "end": 591}, "6": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 678, "end": 688}, "7": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 678, "end": 696}, "8": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 701, "end": 710}, "9": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 701, "end": 718}, "10": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 698, "end": 699}, "11": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 657, "end": 668}, "12": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 731, "end": 740}, "13": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 743, "end": 754}, "14": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 741, "end": 742}, "15": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 730, "end": 762}}, "is_native": false}, "3": {"location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 774, "end": 1045}, "definition_location": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 794, "end": 807}, "type_parameters": [], "parameters": [["amount_out#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 808, "end": 818}], ["reserve_in#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 825, "end": 835}], ["reserve_out#0#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 842, "end": 853}]], "returns": [{"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 861, "end": 864}], "locals": [["denominator#1#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 948, "end": 959}], ["numerator#1#0", {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 879, "end": 888}]], "nops": {}, "code_map": {"0": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 892, "end": 902}, "1": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 892, "end": 910}, "2": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 915, "end": 925}, "3": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 915, "end": 933}, "4": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 912, "end": 913}, "5": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 879, "end": 888}, "6": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 963, "end": 974}, "7": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 977, "end": 987}, "8": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 975, "end": 976}, "9": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 962, "end": 996}, "10": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 948, "end": 959}, "11": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 1008, "end": 1017}, "12": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 1020, "end": 1031}, "13": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 1018, "end": 1019}, "14": {"file_hash": [142, 23, 149, 153, 47, 98, 166, 73, 176, 187, 211, 97, 57, 231, 141, 50, 236, 235, 231, 161, 92, 13, 251, 237, 103, 34, 148, 163, 134, 216, 53, 11], "start": 1007, "end": 1039}}, "is_native": false}}, "constant_map": {}}