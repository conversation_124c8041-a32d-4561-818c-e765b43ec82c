---
compiled_package_info:
  package_name: coin_template
  address_alias_instantiation:
    bridge: 000000000000000000000000000000000000000000000000000000000000000b
    coin_template: "0000000000000000000000000000000000000000000000000000000000000000"
    config_registry: 9a5207b564f7e8b0794e044eb81bfce31cb29d2c3b94e94eb3120c45f1ae68b2
    hopdex: 5055ea61493f3555c493d010bb724c57e432ceedff5d2b9598c82013b4fc97bf
    hopfun: 497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    sui: "0000000000000000000000000000000000000000000000000000000000000002"
    sui_system: "0000000000000000000000000000000000000000000000000000000000000003"
  source_digest: 79761F4AF51E6564A087854915E748D924CED8E433DCC0F987E0CB7DE4D69F59
  build_flags:
    dev_mode: false
    test_mode: false
    generate_docs: false
    save_disassembly: false
    install_dir: ~
    force_recompilation: false
    lock_file: "./Move.lock"
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    default_flavor: sui
    default_edition: ~
    deps_as_root: false
    silence_warnings: false
    warnings_are_errors: false
    json_errors: false
    additional_named_addresses: {}
    lint_flag:
      no_lint: false
      lint: false
    modes: []
    implicit_dependencies:
      Bridge:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/bridge
          subst: ~
          digest: ~
          dep_override: true
      MoveStdlib:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/move-stdlib
          subst: ~
          digest: ~
          dep_override: true
      Sui:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/sui-framework
          subst: ~
          digest: ~
          dep_override: true
      SuiSystem:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/sui-system
          subst: ~
          digest: ~
          dep_override: true
    force_lock_file: false
dependencies:
  - Bridge
  - MoveStdlib
  - Sui
  - SuiSystem
  - config_registry
  - hopdex
  - hopfun
