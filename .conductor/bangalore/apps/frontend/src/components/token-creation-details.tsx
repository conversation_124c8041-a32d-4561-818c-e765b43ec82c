'use client';

import { useSuiClient } from '@mysten/dapp-kit';
import { CheckCircle, ExternalLink, Loader2, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';

interface TokenCreationDetailsProps {
  packageId: string;
  bondingCurveId?: string;
  tokenData?: {
    name: string;
    symbol: string;
    description?: string;
    decimals: number;
    totalSupply: number;
    imageUrl?: string;
  };
  onBack: () => void;
}

interface PackageInfo {
  id: string;
  version: string;
  modules: string[];
  digest: string;
}

interface CoinMetadata {
  id: string;
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  iconUrl?: string;
}

interface BondingCurveInfo {
  id: string;
  exists: boolean;
  verified: boolean;
  details?: any;
}

export function TokenCreationDetails({
  packageId,
  bondingCurveId,
  tokenData,
  onBack,
}: TokenCreationDetailsProps) {
  const suiClient = useSuiClient();

  const [packageInfo, setPackageInfo] = useState<PackageInfo | null>(null);
  const [coinMetadata, setCoinMetadata] = useState<CoinMetadata | null>(null);
  const [bondingCurveInfo, setBondingCurveInfo] =
    useState<BondingCurveInfo | null>(null);

  const [loadingPackage, setLoadingPackage] = useState(true);
  const [loadingMetadata, setLoadingMetadata] = useState(true);
  const [loadingBondingCurve, setLoadingBondingCurve] = useState(true);

  const [packageError, setPackageError] = useState<string | null>(null);
  const [metadataError, setMetadataError] = useState<string | null>(null);
  const [bondingCurveError, setBondingCurveError] = useState<string | null>(
    null,
  );

  // Fetch package information
  useEffect(() => {
    const fetchPackageInfo = async () => {
      try {
        setLoadingPackage(true);
        setPackageError(null);

        const packageObject = await suiClient.getObject({
          id: packageId,
          options: {
            showContent: true,
            showType: true,
            showOwner: true,
            showPreviousTransaction: true,
            showStorageRebate: true,
            showDisplay: true,
          },
        });

        if (packageObject.error) {
          throw new Error(
            packageObject.error.code || 'Failed to fetch package',
          );
        }

        const content = packageObject.data?.content;
        if (content?.dataType === 'package' && packageObject.data) {
          setPackageInfo({
            id: packageObject.data.objectId,
            version: packageObject.data.version || 'Unknown',
            modules: content.disassembled
              ? Object.keys(content.disassembled)
              : [],
            digest: packageObject.data.digest,
          });
        } else {
          throw new Error('Object is not a package');
        }
      } catch (error) {
        console.error('Error fetching package info:', error);
        setPackageError(
          error instanceof Error ? error.message : 'Unknown error',
        );
      } finally {
        setLoadingPackage(false);
      }
    };

    fetchPackageInfo();
  }, [packageId, suiClient]);

  // Set coin metadata from tokenData or fetch it
  useEffect(() => {
    if (tokenData) {
      // Use the token data passed from the creation form
      setCoinMetadata({
        id: 'Available after blockchain indexing',
        name: tokenData.name,
        symbol: tokenData.symbol,
        description: tokenData.description || '',
        decimals: tokenData.decimals,
        iconUrl: tokenData.imageUrl,
      });
      setLoadingMetadata(false);
    } else {
      // Fallback: try to fetch metadata (this might not work immediately after creation)
      const fetchCoinMetadata = async () => {
        try {
          setLoadingMetadata(true);
          setMetadataError(null);

          setCoinMetadata({
            id: 'Metadata will be available after transaction indexing',
            name: 'Token Name (from creation)',
            symbol: 'TOKEN',
            description: 'Token Description (from creation)',
            decimals: 6,
            iconUrl: undefined,
          });
        } catch (error) {
          console.error('Error fetching coin metadata:', error);
          setMetadataError(
            error instanceof Error ? error.message : 'Unknown error',
          );
        } finally {
          setLoadingMetadata(false);
        }
      };

      fetchCoinMetadata();
    }
  }, [packageId, suiClient, tokenData]);

  // Fetch bonding curve information
  useEffect(() => {
    const fetchBondingCurveInfo = async () => {
      if (!bondingCurveId) {
        setBondingCurveInfo({
          id: 'N/A',
          exists: false,
          verified: false,
        });
        setLoadingBondingCurve(false);
        return;
      }

      try {
        setLoadingBondingCurve(true);
        setBondingCurveError(null);

        const bondingCurveObject = await suiClient.getObject({
          id: bondingCurveId,
          options: {
            showContent: true,
            showType: true,
          },
        });

        if (bondingCurveObject.error) {
          setBondingCurveInfo({
            id: bondingCurveId,
            exists: false,
            verified: false,
          });
        } else {
          setBondingCurveInfo({
            id: bondingCurveId,
            exists: true,
            verified: true,
            details: bondingCurveObject.data,
          });
        }
      } catch (error) {
        console.error('Error fetching bonding curve info:', error);
        setBondingCurveError(
          error instanceof Error ? error.message : 'Unknown error',
        );
        setBondingCurveInfo({
          id: bondingCurveId,
          exists: false,
          verified: false,
        });
      } finally {
        setLoadingBondingCurve(false);
      }
    };

    fetchBondingCurveInfo();
  }, [bondingCurveId, suiClient]);

  const getExplorerUrl = (objectId: string) => {
    const network = process.env.NEXT_PUBLIC_NETWORK || 'devnet';
    return `https://suiscan.xyz/${network}/object/${objectId}`;
  };

  return (
    <div className="container mx-auto space-y-6 p-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Token Creation Details</h1>
        <Button variant="outline" onClick={onBack}>
          ← Back to Creation
        </Button>
      </div>

      {/* Package Information */}
      <div className="bg-card text-card-foreground rounded-lg border shadow-sm">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="flex items-center gap-2 text-2xl leading-none font-semibold tracking-tight">
            📦 Package Information
            {loadingPackage ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : packageError ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </h3>
        </div>
        <div className="p-6 pt-0">
          {loadingPackage ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading package information...</span>
            </div>
          ) : packageError ? (
            <div className="text-red-500">Error: {packageError}</div>
          ) : packageInfo ? (
            <div className="space-y-3">
              <div>
                <strong>Package ID:</strong>
                <div className="mt-1 flex items-center gap-2">
                  <code className="rounded bg-gray-100 px-2 py-1 text-sm">
                    {packageInfo.id}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      window.open(getExplorerUrl(packageInfo.id), '_blank')
                    }
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div>
                <strong>Version:</strong> {packageInfo.version}
              </div>
              <div>
                <strong>Modules:</strong>{' '}
                {packageInfo.modules.length > 0
                  ? packageInfo.modules.join(', ')
                  : 'No modules found'}
              </div>
              <div>
                <strong>Digest:</strong>
                <code className="ml-2 rounded bg-gray-100 px-2 py-1 text-sm">
                  {packageInfo.digest}
                </code>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Coin Metadata */}
      <div className="bg-card text-card-foreground rounded-lg border shadow-sm">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="flex items-center gap-2 text-2xl leading-none font-semibold tracking-tight">
            🪙 Coin Metadata
            {loadingMetadata ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : metadataError ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </h3>
        </div>
        <div className="p-6 pt-0">
          {loadingMetadata ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading coin metadata...</span>
            </div>
          ) : metadataError ? (
            <div className="text-red-500">Error: {metadataError}</div>
          ) : coinMetadata ? (
            <div className="space-y-3">
              <div>
                <strong>Metadata ID:</strong>
                <div className="mt-1 flex items-center gap-2">
                  <code className="rounded bg-gray-100 px-2 py-1 text-sm">
                    {coinMetadata.id}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      window.open(getExplorerUrl(coinMetadata.id), '_blank')
                    }
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div>
                <strong>Name:</strong> {coinMetadata.name}
              </div>
              <div>
                <strong>Symbol:</strong> {coinMetadata.symbol}
              </div>
              <div>
                <strong>Decimals:</strong> {coinMetadata.decimals}
              </div>
              {tokenData && (
                <div>
                  <strong>Total Supply:</strong>{' '}
                  {tokenData.totalSupply.toLocaleString()}
                </div>
              )}
              {coinMetadata.description && (
                <div>
                  <strong>Description:</strong> {coinMetadata.description}
                </div>
              )}
              {coinMetadata.iconUrl && (
                <div>
                  <strong>Icon URL:</strong>
                  <a
                    href={coinMetadata.iconUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-2 text-blue-500 hover:underline"
                  >
                    {coinMetadata.iconUrl}
                  </a>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>

      {/* Bonding Curve Information */}
      <div className="bg-card text-card-foreground rounded-lg border shadow-sm">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="flex items-center gap-2 text-2xl leading-none font-semibold tracking-tight">
            📈 Bonding Curve Verification
            {loadingBondingCurve ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : bondingCurveError ? (
              <XCircle className="h-4 w-4 text-red-500" />
            ) : bondingCurveInfo?.verified ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-yellow-500" />
            )}
          </h3>
        </div>
        <div className="p-6 pt-0">
          {loadingBondingCurve ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Verifying bonding curve...</span>
            </div>
          ) : bondingCurveError ? (
            <div className="text-red-500">Error: {bondingCurveError}</div>
          ) : bondingCurveInfo ? (
            <div className="space-y-3">
              <div>
                <strong>Bonding Curve ID:</strong>
                <div className="mt-1 flex items-center gap-2">
                  <code className="rounded bg-gray-100 px-2 py-1 text-sm">
                    {bondingCurveInfo.id}
                  </code>
                  {bondingCurveInfo.exists && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        window.open(
                          getExplorerUrl(bondingCurveInfo.id),
                          '_blank',
                        )
                      }
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
              <div>
                <strong>Status:</strong>
                <span
                  className={`ml-2 rounded px-2 py-1 text-sm ${
                    bondingCurveInfo.verified
                      ? 'bg-green-100 text-green-800'
                      : bondingCurveInfo.exists
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}
                >
                  {bondingCurveInfo.verified
                    ? 'Verified ✓'
                    : bondingCurveInfo.exists
                      ? 'Exists but not verified'
                      : 'Not found'}
                </span>
              </div>
              {bondingCurveInfo.details && (
                <div>
                  <strong>Type:</strong>
                  <code className="ml-2 rounded bg-gray-100 px-2 py-1 text-sm">
                    {bondingCurveInfo.details.type}
                  </code>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>

      <div className="bg-border h-[1px] w-full" />

      <div className="text-center text-sm text-gray-500">
        Token successfully created and verified on the Sui blockchain!
      </div>
    </div>
  );
}
