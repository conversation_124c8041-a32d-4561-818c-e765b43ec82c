'use client';

import { useState } from 'react';

import { LaunchTokenDialog } from '@/components/dialogs/launch-token-dialog';
import { TokenCreationDetails } from '@/components/token-creation-details';

interface TokenCreationResult {
  packageId: string;
  bondingCurveId?: string;
  tokenData?: {
    name: string;
    symbol: string;
    description?: string;
    decimals: number;
    totalSupply: number;
    imageUrl?: string;
  };
}

export default function TestTokenPage() {
  const [open, setOpen] = useState(true);
  const [tokenCreationResult, setTokenCreationResult] =
    useState<TokenCreationResult | null>(null);

  const handleTokenCreated = (result: TokenCreationResult) => {
    setTokenCreationResult(result);
    setOpen(false);
  };

  const handleBackToCreation = () => {
    setTokenCreationResult(null);
    setOpen(true);
  };

  // If we have token creation results, show the details page
  if (tokenCreationResult) {
    return (
      <TokenCreationDetails
        packageId={tokenCreationResult.packageId}
        bondingCurveId={tokenCreationResult.bondingCurveId}
        tokenData={tokenCreationResult.tokenData}
        onBack={handleBackToCreation}
      />
    );
  }

  // Otherwise show the creation flow
  return (
    <div className="container mx-auto p-8">
      <h1 className="mb-4 text-2xl font-bold">Test Token Creation</h1>
      <p className="mb-4">This page is for testing the token creation flow.</p>

      <button
        className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        onClick={() => setOpen(true)}
      >
        Open Token Creation Dialog
      </button>

      <LaunchTokenDialog
        open={open}
        onOpenChange={setOpen}
        onTokenCreated={handleTokenCreated}
      />

      <div className="mt-8 rounded bg-gray-100 p-4">
        <h2 className="mb-2 font-semibold">Debug Information:</h2>
        <p>Network: {process.env.NEXT_PUBLIC_NETWORK}</p>
        <p>Check browser console for transaction details</p>
      </div>
    </div>
  );
}
