import { getFullnodeUrl } from '@mysten/sui/client';

export type Network = 'devnet' | 'testnet' | 'mainnet';

interface ContractConfig {
  hopfunPackageId: string;
  memeConfigId: string;
  hopfunAdminCap: string;
  hopdexPackageId: string;
  hopdexConfigId: string;
  registryId?: string; // Config Registry ID (optional for backward compatibility)
  registryPackageId?: string; // Config Registry Package ID
  useRegistry?: boolean; // Flag to indicate if registry should be used
}

interface NetworkConfig {
  network: Network;
  rpcUrl: string;
  contracts: ContractConfig;
}

class NetworkConfigService {
  private static instance: NetworkConfigService;
  private currentNetwork: Network;

  private constructor() {
    const envNetwork = process.env.NEXT_PUBLIC_NETWORK ?? 'devnet';
    this.currentNetwork = this.validateNetwork(envNetwork);
  }

  static getInstance(): NetworkConfigService {
    NetworkConfigService.instance = new NetworkConfigService();
    return NetworkConfigService.instance;
  }

  private validateNetwork(network: string): Network {
    const validNetworks: Network[] = ['devnet', 'testnet', 'mainnet'];
    if (validNetworks.includes(network as Network)) {
      return network as Network;
    }
    console.warn(`Invalid network: ${network}, defaulting to devnet`);
    return 'devnet';
  }

  getCurrentNetwork(): Network {
    return this.currentNetwork;
  }

  setNetwork(network: Network): void {
    this.currentNetwork = network;
  }

  getNetworkConfig(): NetworkConfig {
    const network = this.getCurrentNetwork();
    const contracts = this.getContractConfig(network);
    const rpcUrl = this.getRpcUrl(network);

    return {
      network,
      rpcUrl,
      contracts,
    };
  }

  private getRpcUrl(network: Network): string {
    // Use the SUI SDK's built-in RPC URLs
    return getFullnodeUrl(network);
  }

  private getContractConfig(network: Network): ContractConfig {
    switch (network) {
      case 'devnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET ??
            '0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac',
          memeConfigId:
            process.env.NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET ??
            '0x864be1505bd5f86cae41741293d02860039007ebf2eeb1374c3dfebc246251d2',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_DEVNET ??
            '0xb904399ad9196a3f8fff2e45fabcb2565c966dc12771a37f720f15161adb282e',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_DEVNET ??
            '0x5055ea61493f3555c493d010bb724c57e432ceedff5d2b9598c82013b4fc97bf',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_DEVNET ??
            '0x11231b125c38dba00f5f092a721a976eb19c5fa8084ac34b4e5da52e5b160631',
          registryId:
            process.env.NEXT_PUBLIC_REGISTRY_ID_DEVNET ??
            '0x5e09c27e66fc5f1fd9f2d6c4b5d18610a3edd1348b097f41ba596dd92d21cf52',
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET ??
            '0x9a5207b564f7e8b0794e044eb81bfce31cb29d2c3b94e94eb3120c45f1ae68b2',
          useRegistry: process.env.NEXT_PUBLIC_USE_REGISTRY === 'true',
        };

      case 'testnet':
        // Return devnet values for testnet as fallback for now
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_TESTNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_TESTNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_TESTNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_TESTNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_TESTNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_TESTNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_TESTNET ?? undefined,
          useRegistry: false,
        };

      case 'mainnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_MAINNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_MAINNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_MAINNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_MAINNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_MAINNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_MAINNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_MAINNET ?? undefined,
          useRegistry: false,
        };

      default:
        throw new Error(`Unsupported network: ${network as string}`);
    }
  }

  isProductionNetwork(): boolean {
    return this.currentNetwork === 'mainnet';
  }

  isDevelopmentNetwork(): boolean {
    return (
      this.currentNetwork === 'devnet' || this.currentNetwork === 'testnet'
    );
  }
}

export const networkConfigService = NetworkConfigService.getInstance();
