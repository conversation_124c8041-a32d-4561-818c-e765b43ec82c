/**
 * Utility functions to help reset wallet state and clear cached data
 * This is particularly useful for resolving ZKLogin signature issues
 */

/**
 * Clear all wallet-related data from localStorage
 * This helps resolve ZKLogin signature caching issues
 */
export function clearWalletCache(): void {
  try {
    // Clear dapp-kit specific storage
    localStorage.removeItem('sui-dapp-kit:wallet-connection-info');
    localStorage.removeItem('sui-dapp-kit:last-connected-wallet-name');
    localStorage.removeItem('sui-dapp-kit:auto-connect-enabled');

    // Clear any other wallet-related storage
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.includes('wallet') ||
          key.includes('zklogin') ||
          key.includes('sui-') ||
          key.includes('dapp-kit'))
      ) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => localStorage.removeItem(key));

    console.log('Wallet cache cleared successfully');
  } catch (error) {
    console.error('Failed to clear wallet cache:', error);
  }
}

/**
 * Clear sessionStorage wallet data
 */
export function clearWalletSessionStorage(): void {
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (
        key &&
        (key.includes('wallet') ||
          key.includes('zklogin') ||
          key.includes('sui-') ||
          key.includes('dapp-kit'))
      ) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => sessionStorage.removeItem(key));

    console.log('Wallet session storage cleared successfully');
  } catch (error) {
    console.error('Failed to clear wallet session storage:', error);
  }
}

/**
 * Complete wallet reset - clears all cached data
 */
export function resetWallet(): void {
  clearWalletCache();
  clearWalletSessionStorage();

  // Force a page reload to ensure clean state
  if (typeof window !== 'undefined') {
    window.location.reload();
  }
}

/**
 * Check if the current error is related to ZKLogin signature issues
 */
export function isZKLoginSignatureError(error: any): boolean {
  const errorMessage = error?.message || error?.details?.message || '';

  return (
    errorMessage.includes('ZKLogin max epoch too large') ||
    errorMessage.includes('Invalid user signature') ||
    errorMessage.includes('Signature is not valid')
  );
}

/**
 * Get user-friendly instructions for resolving ZKLogin issues
 */
export function getZKLoginErrorInstructions(): string {
  return `
To fix this ZKLogin signature issue:

1. Disconnect your wallet
2. Clear browser cache (or use the "Reset Wallet" button)
3. Reconnect your wallet
4. Try the transaction again

This error typically occurs when your wallet has cached an outdated signature.
  `.trim();
}
